import 'package:flutter/material.dart';
import 'package:wicker/screens/create_business_screen.dart';
import 'package:wicker/screens/edit_profile_screen.dart';
import 'package:wicker/screens/inventory_screen.dart';
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/services/config_service.dart'; // Import ConfigService
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/user_contributions_tab.dart';
import 'package:wicker/widgets/user_playlists_tab.dart';
import 'package:wicker/services/user_service.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class HubScreen extends StatefulWidget {
  final String? userId;
  const HubScreen({super.key, this.userId});

  @override
  _HubScreenState createState() => _HubScreenState();
}

class _HubScreenState extends State<HubScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final UserService _userService = UserService();
  final AuthService _authService = AuthService();
  late Future<Map<String, dynamic>> _profileFuture;
  final EcommerceService _ecommerceService = EcommerceService();
  String? _currentUserId;

  // --- THE FIX: Add ConfigService instance ---
  final ConfigService _configService = ConfigService.instance;
  // --- End of FIX ---

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _profileFuture = _userService.getMyProfile();
    _initializeProfile();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Determines which profile to fetch and loads it.
  void _initializeProfile() async {
    // Get the ID of the user who is currently logged in.
    final token = await _authService.getAccessToken();
    if (token != null) {
      // You would typically decode the JWT to get the ID.
      // For simplicity, we'll assume a method exists in AuthService.
      _currentUserId = _authService.getUserIdFromToken(token);
    }

    setState(() {
      if (widget.userId != null) {
        // If a userId is passed, fetch that user's profile.
        _profileFuture = _userService.getUserProfile(widget.userId!);
      } else {
        // Otherwise, fetch the logged-in user's own profile.
        _profileFuture = _userService.getMyProfile();
      }
    });
  }

  /// Refreshes the profile data from the server.
  void _refreshProfile() {
    setState(() {
      _profileFuture = _userService.getMyProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      body: FutureBuilder<Map<String, dynamic>>(
        future: _profileFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError || !snapshot.hasData) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Could not load profile.'),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _refreshProfile,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final profileData = snapshot.data!;

          return NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  title: Text(
                    profileData['username'] ?? 'Profile',
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  backgroundColor: Colors.white,
                  elevation: 0,
                  pinned: true,
                  actions: [
                    IconButton(
                      icon: const Icon(
                        EvaIcons.settings2Outline,
                        color: Colors.black,
                      ),
                      onPressed: () {
                        /* Nav to settings */
                      },
                    ),
                  ],
                  bottom: PreferredSize(
                    preferredSize: const Size.fromHeight(4.0),
                    child: Container(color: Colors.black, height: 3.0),
                  ),
                ),
                SliverToBoxAdapter(child: _buildProfileHeader(profileData)),
              ];
            },
            body: Column(
              children: [
                NeuCard(
                  margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  padding: EdgeInsets.zero,
                  child: TabBar(
                    controller: _tabController,
                    indicator: const BoxDecoration(
                      color: Color(0xFFFFE66D),
                      border: Border(
                        bottom: BorderSide(color: Colors.black, width: 3.0),
                      ),
                    ),
                    indicatorSize: TabBarIndicatorSize.tab,
                    labelColor: Colors.black,
                    unselectedLabelColor: Colors.grey.shade700,
                    labelStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    tabs: const [
                      Tab(icon: Icon(EvaIcons.grid), text: 'Contributions'),
                      Tab(icon: Icon(EvaIcons.bookmark), text: 'Lists'),
                    ],
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: const [
                      UserContributionsTab(),
                      UserPlaylistsTab(),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader(Map<String, dynamic> profile) {
    // --- THE FIX: Use a FutureBuilder to get the baseUrl ---
    return FutureBuilder<String>(
      future: _configService.getBaseUrl(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          // Show a placeholder while the base URL is loading
          return const SizedBox(
            height: 200,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final baseUrl = snapshot.data!;
        String profilePicPath = profile['profile_pic_url']?.toString() ?? '';
        String profilePicUrl = '';

        if (profilePicPath.isNotEmpty) {
          profilePicUrl = '$baseUrl/${profilePicPath.replaceAll('\\', '/')}';
        } else {
          profilePicUrl =
              'https://i.pravatar.cc/150?u=${profile['_id']?['\$oid']}';
        }
        // --- End of FIX ---

        final String points = (profile['points'] ?? 0).toString();
        final String followers = (profile['followers_count'] ?? 0).toString();
        final String following = (profile['following_count'] ?? 0).toString();

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  NeuCard(
                    margin: EdgeInsets.zero,
                    padding: const EdgeInsets.all(4),
                    child: CircleAvatar(
                      radius: 40,
                      backgroundImage: NetworkImage(profilePicUrl),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatColumn("Points", points),
                        _buildStatColumn("Followers", followers),
                        _buildStatColumn("Following", following),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                profile['username'] ?? '',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                profile['bio'] ?? 'No bio yet.',
                style: TextStyle(color: Colors.grey[800], fontSize: 16),
              ),
              const SizedBox(height: 16),
              _buildActionButtons(profile),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatColumn(String label, String count) {
    return Expanded(
      child: NeuCard(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              count,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  /// --- REFACTORED: This widget is now smarter ---
  Widget _buildActionButtons(Map<String, dynamic> profileData) {
    final profileId = profileData['_id']['\$oid'];

    // --- NEW LOGIC: Show different buttons for your own vs. other profiles ---
    if (profileId == _currentUserId || widget.userId == null) {
      // Show "Edit Profile" and "My Business" for your own profile
      return Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                /* Navigate to EditProfileScreen */
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        EditProfileScreen(profileData: profileData),
                  ),
                );
              },
              child: const NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Center(
                  child: Text(
                    "Edit Profile",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: GestureDetector(
              onTap: () {
                // Check if the user has a business
                _ecommerceService.getMyBusiness().then((business) {
                  if (business != null) {
                    // User has a business, navigate to InventoryScreen
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            InventoryScreen(businessData: business),
                      ),
                    );
                  } else {
                    // User doesn't have a business, navigate to CreateBusinessScreen
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CreateBusinessScreen(),
                      ),
                    );
                  }
                });
                /* Navigate to CreateBusinessScreen or InventoryScreen */
              },
              child: const NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.symmetric(vertical: 12),
                backgroundColor: Color(0xFF00D2D3),
                child: Center(
                  child: Text(
                    "My Business",
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      // Show "Follow" and "Message" buttons for other users' profiles
      return Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                /* TODO: Implement Follow/Unfollow logic */
              },
              child: const NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.symmetric(vertical: 12),
                backgroundColor: Color(0xFF6C5CE7), // Vibrant Blue
                child: Center(
                  child: Text(
                    "Follow",
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: GestureDetector(
              onTap: () {
                /* TODO: Implement messaging */
              },
              child: const NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Center(
                  child: Text(
                    "Message",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    }
  }
}
