import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:wicker/screens/business_detail_screen.dart';
import 'package:wicker/screens/place_detail_screen.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart';
import 'package:wicker/services/search_service.dart';
import 'package:wicker/widgets/business_search_result_tile.dart';
import 'package:wicker/widgets/custom_map_marker.dart';
import 'package:wicker/widgets/media_player.dart';
import 'package:wicker/widgets/place_detail_card.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wicker/screens/detail_scroll_viewer.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:geolocator/geolocator.dart';

class ExploreScreen extends StatefulWidget {
  const ExploreScreen({super.key});

  @override
  _ExploreScreenState createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> {
  final PlacesService _placesService = PlacesService();
  final ConfigService _configService = ConfigService.instance;
  final SearchService _searchService = SearchService();
  late Future<List<dynamic>> _dataFuture;
  final MapController _mapController = MapController();
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;

  List<Map<String, dynamic>> _searchResults = [];
  String? _searchSummary;
  bool _isSearching = false;
  bool _isMapView = true;
  Map<String, dynamic>? _selectedPlace;
  String _activeFilter = 'All';
  LatLng? _currentUserLocation; // Store user's location

  final List<Map<String, dynamic>> _categories = [
    {'name': 'All', 'icon': Icons.public, 'color': const Color(0xFF6C5CE7)},
    {
      'name': 'Restaurant',
      'icon': Icons.restaurant_menu,
      'color': const Color(0xFFFF6B6B),
    },
    {
      'name': 'Cafe',
      'icon': Icons.local_cafe,
      'color': const Color(0xFF4ECDC4),
    },
    {'name': 'Shop', 'icon': Icons.store, 'color': const Color(0xFF00D2D3)},
    {'name': 'Art', 'icon': Icons.palette, 'color': const Color(0xFFFFE66D)},
  ];

  @override
  void initState() {
    super.initState();
    _dataFuture = Future.wait([
      _placesService.getPlaces(),
      _configService.getBaseUrl(),
    ]);
    _updateUserLocation(); // Get location on screen init
  }

  @override
  void dispose() {
    _searchController.dispose();
    _mapController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  Future<void> _updateUserLocation() async {
    try {
      bool serviceEnabled;
      LocationPermission permission;

      serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return;
      }
      permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return;
        }
      }
      Position position = await Geolocator.getCurrentPosition();
      if (mounted) {
        setState(() {
          _currentUserLocation = LatLng(position.latitude, position.longitude);
        });
      }
    } catch (e) {
      debugPrint("Could not get user location: $e");
    }
  }

  Future<void> _centerOnUserLocation() async {
    await _updateUserLocation();
    if (_currentUserLocation != null) {
      _mapController.move(_currentUserLocation!, 14.0);
    }
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 750), () async {
      // Increased debounce duration
      if (query.trim().isEmpty) {
        setState(() {
          _searchResults = [];
          _searchSummary = null;
          _isSearching = false;
        });
        return;
      }
      setState(() => _isSearching = true);

      await _updateUserLocation();

      try {
        final results = await _searchService.search(
          query,
          location: _currentUserLocation,
        );
        if (mounted) {
          setState(() {
            _searchResults = List<Map<String, dynamic>>.from(
              results['results'],
            );
            _searchSummary = results['summary'];
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _searchSummary = "Sorry, we couldn't complete that search.";
            _searchResults = [];
          });
        }
        debugPrint("Search error: $e");
      } finally {
        if (mounted) {
          setState(() => _isSearching = false);
        }
      }
    });
  }

  List<Marker> _buildMarkers(List<Map<String, dynamic>> places) {
    return places
        .where(
          (item) =>
              item['content_type'] == 'place' &&
              item['location'] != null &&
              (_activeFilter == 'All' || item['category'] == _activeFilter),
        )
        .map((place) {
          final coords = place['location']['coordinates'];
          return Marker(
            width: 80.0,
            height: 80.0,
            point: LatLng(coords[1], coords[0]),
            child: GestureDetector(
              onTap: () => setState(() => _selectedPlace = place),
              child: CustomMapMarker(category: place['category']),
            ),
          );
        })
        .toList();
  }

  Map<String, List<Map<String, dynamic>>> _prepareDataForViewer(
    List<Map<String, dynamic>> allContent,
  ) {
    final Map<String, List<Map<String, dynamic>>> grouped = {};

    for (var item in allContent) {
      final contentType = item['content_type'] ?? 'post';
      final category = contentType == 'place'
          ? item['category'] as String? ?? 'Other'
          : 'Posts';

      Map<String, dynamic> processedItem = Map.from(item);

      final authorDetails =
          item['author_details'] as Map<String, dynamic>? ?? {};
      processedItem['authorName'] = authorDetails['username'] ?? 'Unknown User';

      if (grouped.containsKey(category)) {
        grouped[category]!.add(processedItem);
      } else {
        grouped[category] = [processedItem];
      }
    }
    return grouped;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          title: const Text(
            'Explore Accra',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: IconButton(
                icon: Icon(
                  _isMapView ? EvaIcons.grid : EvaIcons.map,
                  color: Colors.black,
                ),
                onPressed: () => setState(() => _isMapView = !_isMapView),
              ),
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: FutureBuilder<List<dynamic>>(
        future: _dataFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError ||
              !snapshot.hasData ||
              snapshot.data!.isEmpty) {
            return Center(
              child: Text(
                'Error: ${snapshot.error ?? "Could not load content."}',
              ),
            );
          }

          final allContent = snapshot.data![0] as List<Map<String, dynamic>>;
          final baseUrl = snapshot.data![1] as String;

          final markers = _buildMarkers(allContent);
          final groupedAndProcessedData = _prepareDataForViewer(allContent);

          return Stack(
            children: [
              _isMapView
                  ? _buildInteractiveMapView(markers)
                  : _buildDiscoveryGrid(
                      allContent,
                      groupedAndProcessedData,
                      baseUrl,
                    ),
              Column(
                children: [
                  _buildSearchBar(),
                  if (_isSearching)
                    const LinearProgressIndicator(
                      backgroundColor: Colors.transparent,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Color(0xFF6C5CE7),
                      ),
                    ),
                  if (_searchController.text.trim().isNotEmpty)
                    _buildSearchResultsOverlay(),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      child: NeuCard(
        margin: EdgeInsets.zero,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: TextField(
          controller: _searchController,
          onChanged: _onSearchChanged,
          decoration: InputDecoration(
            icon: const Icon(EvaIcons.search),
            hintText: 'e.g., plumbers near me...',
            border: InputBorder.none,
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(EvaIcons.closeCircle),
                    onPressed: () {
                      _searchController.clear();
                      _onSearchChanged('');
                    },
                  )
                : null,
          ),
        ),
      ),
    );
  }

  Widget _buildSearchResultsOverlay() {
    return Expanded(
      child: Container(
        color: const Color(0xFFFEF7F0).withOpacity(0.95),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (_searchSummary != null)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: NeuCard(
                  backgroundColor: const Color(0xFFFFE66D),
                  child: Text(
                    _searchSummary!,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            Expanded(
              child: _searchResults.isEmpty && !_isSearching
                  ? const Center(child: Text('No results found.'))
                  : ListView.builder(
                      padding: EdgeInsets.zero,
                      itemCount: _searchResults.length,
                      itemBuilder: (context, index) {
                        final result = _searchResults[index];
                        return _buildSearchResultTile(result);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResultTile(Map<String, dynamic> result) {
    final collection = result['collection'];

    switch (collection) {
      case 'businesses':
        return BusinessSearchResultTile(
          businessData: result,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    BusinessDetailScreen(businessData: result),
              ),
            );
          },
        );
      case 'places':
        return GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    PlaceDetailScreen(placeId: result['_id']['\$oid']),
              ),
            );
          },
          child: NeuCard(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: ListTile(
              leading: const Icon(EvaIcons.pin, color: Color(0xFFFF6B6B)),
              title: Text(result['name'] ?? 'Unnamed Place'),
              subtitle: Text(result['category'] ?? 'Place'),
            ),
          ),
        );
      default:
        return NeuCard(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            title: Text(result['name'] ?? result['product_name'] ?? 'Result'),
            subtitle: Text('in $collection'),
          ),
        );
    }
  }

  Widget _buildInteractiveMapView(List<Marker> markers) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.all(4.0),
          child: NeuCard(
            margin: EdgeInsets.zero,
            padding: EdgeInsets.zero,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(13),
              child: Stack(
                children: [
                  FlutterMap(
                    mapController: _mapController,
                    options: MapOptions(
                      initialCenter: const LatLng(5.6037, -0.1870),
                      initialZoom: 12.0,
                      onTap: (_, __) => setState(() => _selectedPlace = null),
                    ),
                    children: [
                      TileLayer(
                        urlTemplate:
                            'https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png',
                        subdomains: const ['a', 'b', 'c', 'd'],
                      ),
                      MarkerLayer(markers: markers),
                    ],
                  ),
                  _buildFilterChips(),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          bottom: _selectedPlace == null ? 32 : 220,
          right: 16,
          child: GestureDetector(
            onTap: _centerOnUserLocation,
            child: const NeuCard(
              padding: EdgeInsets.all(12),
              child: Icon(Icons.my_location),
            ),
          ),
        ),
        AnimatedPositioned(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          bottom: _selectedPlace == null ? -250 : 0,
          left: 0,
          right: 0,
          child: PlaceDetailCard(
            placeData: _selectedPlace ?? {},
            onClose: () => setState(() => _selectedPlace = null),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterChips() {
    return Positioned(
      top: 16,
      left: 0,
      right: 0,
      child: SizedBox(
        height: 50,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            final bool isSelected = _activeFilter == category['name'];
            return GestureDetector(
              onTap: () {
                setState(() {
                  _activeFilter = category['name'];
                  _selectedPlace = null;
                });
              },
              child: NeuCard(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                backgroundColor: isSelected ? category['color'] : Colors.white,
                child: Row(
                  children: [
                    Icon(
                      category['icon'],
                      color: isSelected ? Colors.white : Colors.black,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      category['name'],
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildDiscoveryGrid(
    List<Map<String, dynamic>> allContent,
    Map<String, List<Map<String, dynamic>>> groupedData,
    String baseUrl,
  ) {
    return MasonryGridView.count(
      crossAxisCount: 2,
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      padding: const EdgeInsets.all(16.0),
      itemCount: allContent.length,
      itemBuilder: (context, index) {
        final item = allContent[index];
        final contentType = item['content_type'];
        final String title = item['text_content'] ?? item['name'] ?? 'Untitled';
        Map<String, dynamic> mediaDataForPlayer;

        if (contentType == 'post') {
          final List<dynamic> mediaList = item['media'] as List<dynamic>? ?? [];
          if (mediaList.isEmpty) {
            return NeuCard(child: Center(child: Text(title)));
          }
          mediaDataForPlayer = mediaList.first as Map<String, dynamic>;
        } else {
          final List<dynamic> photoList =
              item['photos'] as List<dynamic>? ?? [];
          if (photoList.isEmpty) {
            return NeuCard(child: Center(child: Text(title)));
          }
          mediaDataForPlayer = {
            'path': photoList.first,
            'type': 'image',
            'aspect_ratio': 1.0,
          };
        }

        return GestureDetector(
          onTap: () {
            final category = contentType == 'place'
                ? item['category'] as String? ?? 'Other'
                : 'Posts';

            final categoryList = groupedData[category] ?? [];
            final categoryKeys = groupedData.keys.toList();
            final categoryIndex = categoryKeys.indexOf(category);

            final itemId = item['_id'];
            final itemIndex = categoryList.indexWhere((listItem) {
              final listItemId = listItem['_id'];

              String normalizedItemId = '';
              String normalizedListItemId = '';

              if (itemId is Map && itemId.containsKey('\$oid')) {
                normalizedItemId = itemId['\$oid'].toString();
              } else if (itemId is String) {
                normalizedItemId = itemId;
              } else {
                normalizedItemId = itemId.toString();
              }

              if (listItemId is Map && listItemId.containsKey('\$oid')) {
                normalizedListItemId = listItemId['\$oid'].toString();
              } else if (listItemId is String) {
                normalizedListItemId = listItemId;
              } else {
                normalizedListItemId = listItemId.toString();
              }

              return normalizedItemId == normalizedListItemId;
            });

            if (itemIndex != -1 && categoryIndex != -1) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DetailScrollViewer(
                    allCategoriesData: groupedData,
                    initialCategoryIndex: categoryIndex,
                    initialItemIndex: itemIndex,
                  ),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Unable to open item details. Please try again.',
                  ),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: MediaPlayer(mediaData: mediaDataForPlayer, baseUrl: baseUrl),
          ),
        );
      },
    );
  }
}
