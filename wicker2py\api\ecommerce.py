import json
from flask import Response, logging, request, jsonify, Blueprint, request
from werkzeug.utils import secure_filename
import os
import uuid
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime

ecommerce_bp = Blueprint('ecommerce_bp', __name__)

@ecommerce_bp.route('/business/my-business', methods=['GET'])
@jwt_required()
def get_my_business():
    """Checks for and returns the current user's business."""
    db = ecommerce_bp.db
    businesses_collection = db.businesses
    current_user_id = ObjectId(get_jwt_identity())
    

    business = businesses_collection.find_one({"owner_id": current_user_id})
    print('business', business)

    if not business:
        return jsonify({"msg": "No business found for this user"}), 404
        
    return json.loads(json_util.dumps(business)), 200




# --- NEW: Endpoint to search for businesses ---
@ecommerce_bp.route('/business/search', methods=['GET'])
@jwt_required()
def search_businesses():
    """Searches for businesses by name or description."""
    db = ecommerce_bp.db
    query = request.args.get('q', '') # Get search query from URL parameter 'q'

    print('query', query)   

    if not query:
        return jsonify([]), 200 # Return empty list if no query

    # Perform a case-insensitive text search on name and description
    businesses = list(db.businesses.find({
        "$text": {"$search": query}
    }))

    print('businesses', businesses)   

    # Note: For this to work, you must create a text index in your MongoDB 'businesses' collection.
    # Connect to your database and run:
    # db.businesses.createIndex({ "business_name": "text", "description": "text" })

    return Response(json_util.dumps(businesses), mimetype='application/json'), 200


# --- NEW: Get business by owner ID ---
@ecommerce_bp.route('/business/owner/<owner_id>', methods=['GET'])
@jwt_required()
def get_business_by_owner(owner_id):
    """Returns the business associated with a specific owner ID."""
    db = ecommerce_bp.db
    business = db.businesses.find_one({"owner_id": ObjectId(owner_id)})
    if not business:
        return jsonify({"msg": "No business found for this user"}), 404
    return json.loads(json_util.dumps(business)), 200

# --- NEW: Get all products for a specific business ---
@ecommerce_bp.route('/business/<business_id>/products', methods=['GET'])
@jwt_required()
def get_business_products(business_id):
    """Returns all products for a given business ID."""
    db = ecommerce_bp.db
    products = list(db.products.find({"business_id": ObjectId(business_id)}))
    return Response(json_util.dumps(products), mimetype='application/json'), 200


# In flask_api/api/ecommerce.py

# In flask_api/api/ecommerce.py

@ecommerce_bp.route('/business/<business_id>', methods=['GET'])
@jwt_required()
def get_business_details(business_id):
    """Returns the details for a single business, including owner info."""
    db = ecommerce_bp.db
    pipeline = [
        {'$match': {'_id': ObjectId(business_id)}},
        {'$lookup': {
            'from': 'users',
            'localField': 'owner_id',
            'foreignField': '_id',
            'as': 'owner_details'
        }},
        {'$unwind': '$owner_details'},
        {'$project': {'owner_details.password_hash': 0, 'owner_details.email': 0}}
    ]
    business = list(db.businesses.aggregate(pipeline))

    if not business:
        return jsonify({"msg": "Business not found"}), 404
        
    return json.loads(json_util.dumps(business[0])), 200


@ecommerce_bp.route('/business/create', methods=['POST'])
@jwt_required()
def create_business():
    """Creates a new business with a name and images."""
    db = ecommerce_bp.db
    businesses_collection = db.businesses
    current_user_id = ObjectId(get_jwt_identity())
    data = request.form

    if 'business_name' not in data:
        return jsonify({"msg": "Business name is required"}), 400

    # Handle image uploads
    image_paths = []
    if 'images' in request.files:
        images = request.files.getlist('images')
        for image in images:
            if image.filename != '':
                filename = secure_filename(image.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                upload_folder = 'uploads/business'
                if not os.path.exists(upload_folder):
                    os.makedirs(upload_folder)
                
                image_path = os.path.join(upload_folder, unique_filename)
                image.save(image_path)
                image_paths.append(image_path)

    new_business = {
        "owner_id": current_user_id,
        "business_name": data['business_name'],
        "description": data.get('description', ''),
        "images": image_paths,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
    }

    try:
        businesses_collection.insert_one(new_business)
        return jsonify({"msg": "Business created successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    

@ecommerce_bp.route('/product/add', methods=['POST'])
@jwt_required()
def add_product():
    db = ecommerce_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.form

    print('data',data)

    # --- Validation ---
    required_fields = ['business_id', 'product_name', 'price', 'product_type']
    if not all(field in data for field in required_fields):
        print('not all fields were found')
        return jsonify({"msg": "Missing required fields"}), 400



    # Verify the user owns the business they're adding a product to
    business = db.businesses.find_one({
        "_id": ObjectId(data['business_id']),
        "owner_id": current_user_id
    })
    if not business:
        logging.info('business not found')
        return jsonify({"msg": "Business not found or access denied"}), 404

    # --- Media File Handling ---
    media_paths = []
    if 'media' in request.files:
        files = request.files.getlist('media')
        for file in files:
            if file.filename != '':
                filename = secure_filename(file.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                upload_folder = 'uploads/products'
                if not os.path.exists(upload_folder):
                    os.makedirs(upload_folder)
                
                file_path = os.path.join(upload_folder, unique_filename)
                file.save(file_path)
                media_paths.append(file_path)

    # --- Create Product Document ---
    new_product = {
        "business_id": ObjectId(data['business_id']),
        "product_name": data['product_name'],
        "description": data.get('description', ''),
        "price": float(data['price']),
        "product_type": data['product_type'], # 'physical', 'digital', 'service'
        "product_sub_type": data.get('product_sub_type'), # NEW: 'manufactured' or 'handmade'
        "media": media_paths,
        "barcode": data.get('barcode'), # For scannable physical products
        "stock_count": int(data.get('stock_count', 1)),
        "created_at": datetime.datetime.now(datetime.timezone.utc),
    }

    print('new product', new_product)

    try:
        db.products.insert_one(new_product)
        return jsonify({"msg": "Product added successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    


# delete product
@ecommerce_bp.route('/delete-product/<product_id>', methods=['DELETE'])
@jwt_required()
def delete_product(product_id):
    db = ecommerce_bp.db
    products_collection = db.products
    current_user_id = get_jwt_identity()

    try:
        result = products_collection.delete_one({
            '_id': ObjectId(product_id),
            'business_id': ObjectId(current_user_id)
        })
        if result.deleted_count == 1:
            return jsonify({"msg": "Product deleted successfully"}), 200
        else:
            return jsonify({"msg": "Product not found or not authorized"}), 404
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500