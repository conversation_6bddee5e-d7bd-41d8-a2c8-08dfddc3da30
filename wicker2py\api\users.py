import os
import uuid
from flask import Response, jsonify, Blueprint, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from bson import ObjectId, json_util
import json

users_bp = Blueprint('users_bp', __name__)

from flask import Response, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import json

users_bp = Blueprint('users_bp', __name__)

@users_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_my_profile():
    """Fetches the profile data, including follower and following counts."""
    db = users_bp.db
    users_collection = db.users
    follows_collection = db.follows
    current_user_id = ObjectId(get_jwt_identity())

    try:
        # --- THE FIX: Aggregation pipeline to get all data at once ---
        pipeline = [
            {'$match': {'_id': current_user_id}},
            # Lookup who the user is following
            {
                '$lookup': {
                    'from': 'follows',
                    'localField': '_id',
                    'foreignField': 'follower_id',
                    'as': 'following'
                }
            },
            # Lookup who is following the user
            {
                '$lookup': {
                    'from': 'follows',
                    'localField': '_id',
                    'foreignField': 'following_id',
                    'as': 'followers'
                }
            },
            # Add counts and project the final fields
            {
                '$addFields': {
                    'following_count': {'$size': '$following'},
                    'followers_count': {'$size': '$followers'}
                }
            },
            {
                '$project': {
                    'password_hash': 0, # Exclude sensitive data
                    'following': 0,     # Exclude the full arrays
                    'followers': 0
                }
            }
        ]
        # --- End of FIX ---
        
        user_profile_list = list(users_collection.aggregate(pipeline))

        if not user_profile_list:
            return jsonify({"msg": "User not found"}), 404

        return json.loads(json_util.dumps(user_profile_list[0])), 200
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    

# --- NEW: Endpoint to update user profile ---
@users_bp.route('/profile/update', methods=['POST'])
@jwt_required()
def update_my_profile():
    db = users_bp.db
    users_collection = db.users
    current_user_id = ObjectId(get_jwt_identity())
    
    data = request.form
    update_fields = {}

    # Update username and bio from form data
    if 'username' in data and data['username']:
        update_fields['username'] = data['username']
    if 'bio' in data:
        update_fields['bio'] = data['bio']

    # Handle profile picture upload
    if 'profile_pic' in request.files:
        image = request.files['profile_pic']
        if image.filename != '':
            filename = secure_filename(image.filename)
            unique_filename = f"{uuid.uuid4()}_{filename}"
            upload_folder = 'uploads/avatars'
            if not os.path.exists(upload_folder):
                os.makedirs(upload_folder)
            
            image_path = os.path.join(upload_folder, unique_filename)
            image.save(image_path)
            update_fields['profile_pic_url'] = image_path # Store the path

    if not update_fields:
        return jsonify({"msg": "No update fields provided"}), 400

    try:
        users_collection.update_one(
            {'_id': current_user_id},
            {'$set': update_fields}
        )
        return jsonify({"msg": "Profile updated successfully"}), 200
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

@users_bp.route('/my-contributions', methods=['GET'])
@jwt_required()
def get_my_contributions():
    """Fetches a combined list of a user's posts and created places,
    ensuring author_details are always included."""
    db = users_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    try:
        # Pipeline to fetch and format posts
        posts_pipeline = [
            {'$match': {'author_id': current_user_id}},
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'author_id',
                    'foreignField': '_id',
                    'as': 'author_details'
                }
            },
            {'$unwind': '$author_details'},
            {'$addFields': {'contribution_type': 'post'}},
            {'$sort': {'created_at': -1}}
        ]
        user_posts = list(db.posts.aggregate(posts_pipeline))

        # Pipeline to fetch and format places
        places_pipeline = [
            {'$match': {'created_by': current_user_id}},
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'created_by',
                    'foreignField': '_id',
                    'as': 'author_details'
                }
            },
            {'$unwind': '$author_details'},
            {'$addFields': {'contribution_type': 'place'}},
            {'$sort': {'created_at': -1}}
        ]
        user_places = list(db.places.aggregate(places_pipeline))

        # Combine and sort all contributions
        all_contributions = sorted(
            user_posts + user_places,
            key=lambda x: x['created_at'],
            reverse=True
        )

        return Response(json_util.dumps(all_contributions), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    




# --- NEW: Endpoint to get a specific user's public profile ---
@users_bp.route('/<user_id>/profile', methods=['GET'])
@jwt_required()
def get_user_profile(user_id):
    """Fetches public profile data for a given user_id."""
    db = users_bp.db
    # This pipeline is almost identical to get_my_profile,
    # but it uses the user_id from the URL instead of the JWT identity.
    pipeline = [
        {'$match': {'_id': ObjectId(user_id)}},
        {'$lookup': {
            'from': 'follows', 'localField': '_id',
            'foreignField': 'follower_id', 'as': 'following'
        }},
        {'$lookup': {
            'from': 'follows', 'localField': '_id',
            'foreignField': 'following_id', 'as': 'followers'
        }},
        {'$addFields': {
            'following_count': {'$size': '$following'},
            'followers_count': {'$size': '$followers'}
        }},
        {'$project': {'password_hash': 0, 'email': 0, 'following': 0, 'followers': 0}}
    ]
    user_profile_list = list(db.users.aggregate(pipeline))

    if not user_profile_list:
        return jsonify({"msg": "User not found"}), 404

    return json.loads(json_util.dumps(user_profile_list[0])), 200