import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart'; // Import LatLng
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class SearchService {
  final ConfigService _config = ConfigService.instance;
  final WickerHttpClient _client = WickerHttpClient();

  // THE REFACTOR: The method now accepts an optional LatLng parameter.
  Future<Map<String, dynamic>> search(String query, {LatLng? location}) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      // Build the request body
      final Map<String, dynamic> body = {'query': query};
      if (location != null) {
        body['location'] = {
          'lat': location.latitude,
          'lon': location.longitude,
        };
      }

      final response = await _client.post(
        Uri.parse('$baseUrl/api/search/'),
        headers: {'Content-Type': 'application/json; charset=UTF-8'},
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to perform search. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Search service error: $e');
      rethrow;
    }
  }
}
