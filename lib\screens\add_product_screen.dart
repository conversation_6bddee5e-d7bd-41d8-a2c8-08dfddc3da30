// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:wicker/screens/barcode_scanner_screen.dart';
// import 'package:wicker/services/ecommerce_service.dart';
// import 'package:wicker/widgets/neubrutalist_widgets.dart';
// import 'package:wicker/widgets/media_preview_tile.dart';
// import 'package:eva_icons_flutter/eva_icons_flutter.dart';

// class AddProductScreen extends StatefulWidget {
//   final String businessId;
//   const AddProductScreen({super.key, required this.businessId});

//   @override
//   _AddProductScreenState createState() => _AddProductScreenState();
// }

// class _AddProductScreenState extends State<AddProductScreen> {
//   final _formKey = GlobalKey<FormState>();
//   final _nameController = TextEditingController();
//   final _descriptionController = TextEditingController();
//   final _priceController = TextEditingController();
//   final EcommerceService _ecommerceService = EcommerceService();

//   String _productType = 'physical';
//   String? _barcode;
//   final List<XFile> _mediaFiles = [];
//   bool _isLoading = false;

//   Future<void> _scanBarcode() async {
//     final result = await Navigator.push<String>(
//       context,
//       MaterialPageRoute(builder: (context) => const BarcodeScannerScreen()),
//     );

//     if (result != null && mounted) {
//       setState(() => _barcode = result);
//     }
//   }

//   /// --- THE FIX: Shows a modal with options to pick media ---
//   Future<void> _showMediaPickerOptions() async {
//     showModalBottomSheet(
//       context: context,
//       backgroundColor: const Color(0xFFFEF7F0),
//       builder: (context) {
//         return Wrap(
//           children: <Widget>[
//             ListTile(
//               leading: const Icon(EvaIcons.imageOutline),
//               title: const Text('Pick from Gallery'),
//               onTap: () async {
//                 Navigator.pop(context);
//                 final picker = ImagePicker();
//                 final List<XFile> pickedFiles = await picker
//                     .pickMultipleMedia();
//                 setState(() => _mediaFiles.addAll(pickedFiles));
//               },
//             ),
//             ListTile(
//               leading: const Icon(EvaIcons.cameraOutline),
//               title: const Text('Take Photo'),
//               onTap: () async {
//                 Navigator.pop(context);
//                 final picker = ImagePicker();
//                 final XFile? pickedFile = await picker.pickImage(
//                   source: ImageSource.camera,
//                 );
//                 if (pickedFile != null) {
//                   setState(() => _mediaFiles.add(pickedFile));
//                 }
//               },
//             ),
//             ListTile(
//               leading: const Icon(EvaIcons.videoOutline),
//               title: const Text('Record Video'),
//               onTap: () async {
//                 Navigator.pop(context);
//                 final picker = ImagePicker();
//                 final XFile? pickedFile = await picker.pickVideo(
//                   source: ImageSource.camera,
//                 );
//                 if (pickedFile != null) {
//                   setState(() => _mediaFiles.add(pickedFile));
//                 }
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }

//   /// --- End of FIX ---

//   void _removeMedia(int index) {
//     setState(() {
//       _mediaFiles.removeAt(index);
//     });
//   }

//   void _submit() async {
//     if (!_formKey.currentState!.validate() || _mediaFiles.isEmpty) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         const SnackBar(
//           content: Text(
//             'Please fill all fields and add at least one media file.',
//           ),
//         ),
//       );
//       return;
//     }
//     setState(() => _isLoading = true);

//     try {
//       await _ecommerceService.addProduct(
//         businessId: widget.businessId,
//         productName: _nameController.text,
//         description: _descriptionController.text,
//         price: double.parse(_priceController.text),
//         productType: _productType,
//         barcode: _barcode,
//         media: _mediaFiles,
//       );
//       if (mounted) Navigator.pop(context, true);
//     } catch (e) {
//       if (mounted) {
//         ScaffoldMessenger.of(
//           context,
//         ).showSnackBar(SnackBar(content: Text('Error: $e')));
//       }
//     } finally {
//       if (mounted) setState(() => _isLoading = false);
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: const Color(0xFFFEF7F0),
//       appBar: PreferredSize(
//         preferredSize: const Size.fromHeight(60.0),
//         child: AppBar(
//           backgroundColor: Colors.white,
//           elevation: 0,
//           leading: IconButton(
//             icon: const Icon(Icons.arrow_back, color: Colors.black),
//             onPressed: () => Navigator.of(context).pop(),
//           ),
//           title: const Text(
//             'Add New Product',
//             style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
//           ),
//           bottom: PreferredSize(
//             preferredSize: const Size.fromHeight(4.0),
//             child: Container(color: Colors.black, height: 3.0),
//           ),
//         ),
//       ),
//       body: SingleChildScrollView(
//         padding: const EdgeInsets.all(16.0),
//         child: Form(
//           key: _formKey,
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.stretch,
//             children: [
//               const Text(
//                 'Product Name',
//                 style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//               ),
//               const SizedBox(height: 8),
//               NeuCard(
//                 margin: EdgeInsets.zero,
//                 padding: const EdgeInsets.symmetric(horizontal: 16),
//                 child: TextFormField(
//                   controller: _nameController,
//                   decoration: const InputDecoration(border: InputBorder.none),
//                   validator: (value) =>
//                       value!.isEmpty ? 'Please enter a name' : null,
//                 ),
//               ),
//               const SizedBox(height: 24),
//               const Text(
//                 'Product Description',
//                 style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//               ),
//               const SizedBox(height: 8),
//               NeuCard(
//                 margin: EdgeInsets.zero,
//                 padding: const EdgeInsets.symmetric(
//                   horizontal: 16,
//                   vertical: 8,
//                 ),
//                 child: TextFormField(
//                   controller: _descriptionController,
//                   maxLines: 4,
//                   decoration: const InputDecoration(border: InputBorder.none),
//                   validator: (value) =>
//                       value!.isEmpty ? 'Please enter a description' : null,
//                 ),
//               ),
//               const SizedBox(height: 24),
//               const Text(
//                 'Price (GHS)',
//                 style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//               ),
//               const SizedBox(height: 8),
//               NeuCard(
//                 margin: EdgeInsets.zero,
//                 padding: const EdgeInsets.symmetric(horizontal: 16),
//                 child: TextFormField(
//                   controller: _priceController,
//                   decoration: const InputDecoration(border: InputBorder.none),
//                   keyboardType: TextInputType.number,
//                   validator: (value) =>
//                       value!.isEmpty ? 'Please enter a price' : null,
//                 ),
//               ),
//               const SizedBox(height: 24),
//               const Text(
//                 'Product Type',
//                 style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//               ),
//               const SizedBox(height: 8),
//               DropdownButtonFormField<String>(
//                 value: _productType,
//                 items: ['physical', 'digital', 'service']
//                     .map(
//                       (label) =>
//                           DropdownMenuItem(child: Text(label), value: label),
//                     )
//                     .toList(),
//                 onChanged: (value) {
//                   setState(() {
//                     _productType = value ?? 'physical';
//                   });
//                 },
//               ),
//               if (_productType == 'physical') ...[
//                 const SizedBox(height: 24),
//                 GestureDetector(
//                   onTap: _scanBarcode,
//                   child: NeuCard(
//                     margin: EdgeInsets.zero,
//                     backgroundColor: _barcode == null
//                         ? Colors.white
//                         : const Color(0xFF4ECDC4),
//                     child: Padding(
//                       padding: const EdgeInsets.all(12.0),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           Icon(
//                             EvaIcons.maximizeOutline,
//                             color: _barcode == null
//                                 ? Colors.black
//                                 : Colors.white,
//                           ),
//                           const SizedBox(width: 8),
//                           Text(
//                             _barcode == null
//                                 ? 'Scan Barcode (Optional)'
//                                 : 'Barcode Scanned!',
//                             style: TextStyle(
//                               fontWeight: FontWeight.bold,
//                               color: _barcode == null
//                                   ? Colors.black
//                                   : Colors.white,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ),
//                 if (_barcode != null)
//                   Padding(
//                     padding: const EdgeInsets.only(top: 8.0),
//                     child: Center(
//                       child: Text(
//                         'Barcode: $_barcode',
//                         style: const TextStyle(color: Colors.grey),
//                       ),
//                     ),
//                   ),
//               ],
//               const SizedBox(height: 24),
//               const Text(
//                 'Media (Images/Videos)',
//                 style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//               ),
//               const SizedBox(height: 8),
//               _mediaFiles.isEmpty
//                   ? GestureDetector(
//                       onTap: _showMediaPickerOptions,
//                       child: NeuCard(
//                         margin: EdgeInsets.zero,
//                         child: const SizedBox(
//                           height: 100,
//                           child: Center(
//                             child: Column(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 Icon(
//                                   EvaIcons.imageOutline,
//                                   size: 40,
//                                   color: Colors.grey,
//                                 ),
//                                 SizedBox(height: 8),
//                                 Text('Add Media'),
//                               ],
//                             ),
//                           ),
//                         ),
//                       ),
//                     )
//                   : SizedBox(
//                       height: 110,
//                       child: ListView.builder(
//                         scrollDirection: Axis.horizontal,
//                         itemCount: _mediaFiles.length + 1,
//                         itemBuilder: (context, index) {
//                           if (index == _mediaFiles.length) {
//                             return GestureDetector(
//                               onTap:
//                                   _showMediaPickerOptions, // Use the new function
//                               child: const NeuCard(
//                                 child: SizedBox(
//                                   width: 100,
//                                   height: 100,
//                                   child: Center(
//                                     child: Icon(EvaIcons.plus, size: 32),
//                                   ),
//                                 ),
//                               ),
//                             );
//                           }
//                           return MediaPreviewTile(
//                             mediaFile: _mediaFiles[index],
//                             onRemove: () => _removeMedia(index),
//                           );
//                         },
//                       ),
//                     ),
//               const SizedBox(height: 40),
//               GestureDetector(
//                 onTap: _isLoading ? null : _submit,
//                 child: NeuCard(
//                   margin: EdgeInsets.zero,
//                   padding: const EdgeInsets.all(16),
//                   backgroundColor: const Color(0xFF00D2D3),
//                   child: Center(
//                     child: _isLoading
//                         ? const SizedBox(
//                             width: 24,
//                             height: 24,
//                             child: CircularProgressIndicator(
//                               strokeWidth: 3,
//                               color: Colors.white,
//                             ),
//                           )
//                         : const Text(
//                             'Add Product',
//                             style: TextStyle(
//                               color: Colors.white,
//                               fontWeight: FontWeight.bold,
//                               fontSize: 16,
//                             ),
//                           ),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wicker/screens/barcode_scanner_screen.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/media_preview_tile.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class AddProductScreen extends StatefulWidget {
  final String businessId;
  const AddProductScreen({super.key, required this.businessId});

  @override
  _AddProductScreenState createState() => _AddProductScreenState();
}

class _AddProductScreenState extends State<AddProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final EcommerceService _ecommerceService = EcommerceService();

  String _productType = 'physical';
  // NEW: State variable for the product sub-type
  String? _productSubType = 'manufactured';
  String? _barcode;
  final List<XFile> _mediaFiles = [];
  bool _isLoading = false;

  Future<void> _scanBarcode() async {
    final result = await Navigator.push<String>(
      context,
      MaterialPageRoute(builder: (context) => const BarcodeScannerScreen()),
    );

    if (result != null && mounted) {
      setState(() => _barcode = result);
    }
  }

  Future<void> _showMediaPickerOptions() async {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFFFEF7F0),
      builder: (context) {
        return Wrap(
          children: <Widget>[
            ListTile(
              leading: const Icon(EvaIcons.imageOutline),
              title: const Text('Pick from Gallery'),
              onTap: () async {
                Navigator.pop(context);
                final picker = ImagePicker();
                final List<XFile> pickedFiles = await picker
                    .pickMultipleMedia();
                setState(() => _mediaFiles.addAll(pickedFiles));
              },
            ),
            ListTile(
              leading: const Icon(EvaIcons.cameraOutline),
              title: const Text('Take Photo'),
              onTap: () async {
                Navigator.pop(context);
                final picker = ImagePicker();
                final XFile? pickedFile = await picker.pickImage(
                  source: ImageSource.camera,
                );
                if (pickedFile != null) {
                  setState(() => _mediaFiles.add(pickedFile));
                }
              },
            ),
            ListTile(
              leading: const Icon(EvaIcons.videoOutline),
              title: const Text('Record Video'),
              onTap: () async {
                Navigator.pop(context);
                final picker = ImagePicker();
                final XFile? pickedFile = await picker.pickVideo(
                  source: ImageSource.camera,
                );
                if (pickedFile != null) {
                  setState(() => _mediaFiles.add(pickedFile));
                }
              },
            ),
          ],
        );
      },
    );
  }

  void _removeMedia(int index) {
    setState(() {
      _mediaFiles.removeAt(index);
    });
  }

  void _submit() async {
    if (!_formKey.currentState!.validate() || _mediaFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Please fill all fields and add at least one media file.',
          ),
        ),
      );
      return;
    }
    setState(() => _isLoading = true);

    try {
      await _ecommerceService.addProduct(
        businessId: widget.businessId,
        productName: _nameController.text,
        description: _descriptionController.text,
        price: double.parse(_priceController.text),
        productType: _productType,
        productSubType: _productSubType, // NEW: Pass the sub-type
        barcode: _barcode,
        media: _mediaFiles,
      );
      if (mounted) Navigator.pop(context, true);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: $e')));
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text(
            'Add New Product',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Product Name',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              NeuCard(
                margin: EdgeInsets.zero,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(border: InputBorder.none),
                  validator: (value) =>
                      value!.isEmpty ? 'Please enter a name' : null,
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'Product Description',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              NeuCard(
                margin: EdgeInsets.zero,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: TextFormField(
                  controller: _descriptionController,
                  maxLines: 4,
                  decoration: const InputDecoration(border: InputBorder.none),
                  validator: (value) =>
                      value!.isEmpty ? 'Please enter a description' : null,
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'Price (GHS)',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              NeuCard(
                margin: EdgeInsets.zero,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextFormField(
                  controller: _priceController,
                  decoration: const InputDecoration(border: InputBorder.none),
                  keyboardType: TextInputType.number,
                  validator: (value) =>
                      value!.isEmpty ? 'Please enter a price' : null,
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'Product Type',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                value: _productType,
                items: ['physical', 'digital', 'service']
                    .map(
                      (label) =>
                          DropdownMenuItem(child: Text(label), value: label),
                    )
                    .toList(),
                onChanged: (value) {
                  setState(() {
                    _productType = value ?? 'physical';
                    if (_productType == 'physical') {
                      _productSubType = 'manufactured';
                    } else {
                      _productSubType = null;
                      _barcode = null;
                    }
                  });
                },
              ),
              // NEW: Conditional sub-type dropdown
              if (_productType == 'physical') ...[
                const SizedBox(height: 24),
                const Text(
                  'Physical Product Type',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _productSubType,
                  items: ['manufactured', 'handmade']
                      .map(
                        (label) =>
                            DropdownMenuItem(child: Text(label), value: label),
                      )
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _productSubType = value;
                      if (_productSubType == 'handmade') {
                        _barcode = null;
                      }
                    });
                  },
                ),
              ],
              // NEW: Conditional barcode scanner button
              if (_productType == 'physical' &&
                  _productSubType == 'manufactured') ...[
                const SizedBox(height: 24),
                GestureDetector(
                  onTap: _scanBarcode,
                  child: NeuCard(
                    margin: EdgeInsets.zero,
                    backgroundColor: _barcode == null
                        ? Colors.white
                        : const Color(0xFF4ECDC4),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            EvaIcons.maximizeOutline,
                            color: _barcode == null
                                ? Colors.black
                                : Colors.white,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _barcode == null
                                ? 'Scan Barcode (Optional)'
                                : 'Barcode Scanned!',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _barcode == null
                                  ? Colors.black
                                  : Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                if (_barcode != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Center(
                      child: Text(
                        'Barcode: $_barcode',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ),
                  ),
              ],
              const SizedBox(height: 24),
              const Text(
                'Media (Images/Videos)',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              _mediaFiles.isEmpty
                  ? GestureDetector(
                      onTap: _showMediaPickerOptions,
                      child: NeuCard(
                        margin: EdgeInsets.zero,
                        child: const SizedBox(
                          height: 100,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  EvaIcons.imageOutline,
                                  size: 40,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 8),
                                Text('Add Media'),
                              ],
                            ),
                          ),
                        ),
                      ),
                    )
                  : SizedBox(
                      height: 110,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: _mediaFiles.length + 1,
                        itemBuilder: (context, index) {
                          if (index == _mediaFiles.length) {
                            return GestureDetector(
                              onTap:
                                  _showMediaPickerOptions, // Use the new function
                              child: const NeuCard(
                                child: SizedBox(
                                  width: 100,
                                  height: 100,
                                  child: Center(
                                    child: Icon(EvaIcons.plus, size: 32),
                                  ),
                                ),
                              ),
                            );
                          }
                          return MediaPreviewTile(
                            mediaFile: _mediaFiles[index],
                            onRemove: () => _removeMedia(index),
                          );
                        },
                      ),
                    ),
              const SizedBox(height: 40),
              GestureDetector(
                onTap: _isLoading ? null : _submit,
                child: NeuCard(
                  margin: EdgeInsets.zero,
                  padding: const EdgeInsets.all(16),
                  backgroundColor: const Color(0xFF00D2D3),
                  child: Center(
                    child: _isLoading
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 3,
                              color: Colors.white,
                            ),
                          )
                        : const Text(
                            'Add Product',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
